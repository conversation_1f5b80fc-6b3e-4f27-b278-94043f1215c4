#!/usr/bin/env node

/**
 * 测试生产环境构建脚本
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'

const __dirname = dirname(fileURLToPath(import.meta.url))

console.log('🧪 测试生产环境构建...\n')

// 1. 清理并重新构建
console.log('🔨 清理并重新构建...')
try {
  execSync('npm run build', { stdio: 'inherit', cwd: __dirname })
  console.log('✅ 构建完成')
} catch (error) {
  console.log('❌ 构建失败:', error.message)
  process.exit(1)
}

// 2. 检查构建输出
console.log('\n📁 检查构建输出...')
const mainDistPath = join(__dirname, 'packages/main/dist')
const requiredFiles = [
  'index.js',
  'upload-worker.js'
]

for (const file of requiredFiles) {
  const filePath = join(mainDistPath, file)
  const exists = existsSync(filePath)
  console.log(`  ${exists ? '✅' : '❌'} ${file}`)
  
  if (exists && file === 'upload-worker.js') {
    // 检查 Worker 文件内容
    try {
      const content = readFileSync(filePath, 'utf8')
      const hasWorkerThreads = content.includes('worker_threads')
      const hasParentPort = content.includes('parentPort')
      const hasAliOss = content.includes('ali-oss')
      
      console.log(`    ${hasWorkerThreads ? '✅' : '❌'} worker_threads 导入`)
      console.log(`    ${hasParentPort ? '✅' : '❌'} parentPort 使用`)
      console.log(`    ${hasAliOss ? '✅' : '❌'} ali-oss 引用`)
    } catch (error) {
      console.log(`    ❌ 无法读取文件内容: ${error.message}`)
    }
  }
}

// 3. 尝试打包
console.log('\n📦 尝试打包应用...')
try {
  execSync('npm run package', { stdio: 'inherit', cwd: __dirname })
  console.log('✅ 打包完成')
} catch (error) {
  console.log('❌ 打包失败:', error.message)
  console.log('\n💡 可能的问题:')
  console.log('   1. 依赖项缺失')
  console.log('   2. Worker 文件路径问题')
  console.log('   3. 构建配置错误')
  process.exit(1)
}

// 4. 检查打包结果
console.log('\n🔍 检查打包结果...')
const distDir = join(__dirname, 'dist')
if (existsSync(distDir)) {
  console.log('✅ dist 目录存在')
  
  // 运行生产环境调试脚本
  console.log('\n🚀 运行生产环境调试...')
  try {
    execSync('node debug-production.js', { stdio: 'inherit', cwd: __dirname })
  } catch (error) {
    console.log('⚠️  调试脚本执行完成，请查看上面的输出')
  }
} else {
  console.log('❌ dist 目录不存在')
}

console.log('\n✅ 测试完成')
