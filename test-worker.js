#!/usr/bin/env node

/**
 * 测试 Worker 脚本 - 验证 Worker 是否能正常工作
 */

import { Worker } from 'worker_threads'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { existsSync } from 'fs'

const __dirname = dirname(fileURLToPath(import.meta.url))

console.log('🧪 测试 Worker 功能...\n')

// 检查 Worker 脚本是否存在
const workerPath = join(__dirname, 'packages/main/dist/upload-worker.js')
console.log(`📁 Worker 脚本路径: ${workerPath}`)

if (!existsSync(workerPath)) {
  console.log('❌ Worker 脚本不存在，请先运行构建命令: npm run build')
  process.exit(1)
}

console.log('✅ Worker 脚本存在')

// 尝试创建 Worker
console.log('\n🔧 尝试创建 Worker...')

try {
  const worker = new Worker(workerPath)
  console.log('✅ Worker 创建成功')

  // 设置消息处理
  worker.on('message', (message) => {
    console.log('📨 收到 Worker 消息:', message)
  })

  worker.on('error', (error) => {
    console.log('❌ Worker 错误:', error.message)
    console.log('   堆栈:', error.stack)
  })

  worker.on('exit', (code) => {
    console.log(`🏁 Worker 退出，代码: ${code}`)
    if (code === 0) {
      console.log('✅ Worker 正常退出')
    } else {
      console.log('❌ Worker 异常退出')
    }
    process.exit(code)
  })

  // 发送测试消息
  console.log('📤 发送测试消息到 Worker...')
  
  // 等待一下让 Worker 初始化
  setTimeout(() => {
    try {
      worker.postMessage({
        type: 'test',
        data: { message: 'Hello Worker!' }
      })
      console.log('✅ 测试消息发送成功')
    } catch (error) {
      console.log('❌ 发送消息失败:', error.message)
    }
  }, 1000)

  // 5秒后终止 Worker
  setTimeout(() => {
    console.log('\n⏰ 5秒超时，终止 Worker')
    worker.terminate()
  }, 5000)

} catch (error) {
  console.log('❌ 创建 Worker 失败:', error.message)
  console.log('   堆栈:', error.stack)
  
  // 分析错误
  if (error.message.includes('worker_threads')) {
    console.log('\n💡 可能的问题:')
    console.log('   - Node.js 版本不支持 worker_threads')
    console.log('   - worker_threads 模块未正确导入')
  }
  
  if (error.message.includes('Cannot find module')) {
    console.log('\n💡 可能的问题:')
    console.log('   - Worker 脚本中的依赖模块缺失')
    console.log('   - 需要运行 npm install 安装依赖')
  }
  
  process.exit(1)
}
