#!/usr/bin/env node

/**
 * 调试脚本 - 用于诊断 Electron 应用启动问题
 */

import { spawn } from 'child_process'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { existsSync } from 'fs'

const __dirname = dirname(fileURLToPath(import.meta.url))

console.log('🔍 开始诊断 Electron 应用启动问题...\n')

// 1. 检查关键文件是否存在
console.log('📁 检查关键文件:')
const criticalFiles = [
  'packages/entry-point.mjs',
  'packages/main/dist/index.js',
  'packages/main/dist/upload-worker.js',
  'package.json'
]

let allFilesExist = true
for (const file of criticalFiles) {
  const exists = existsSync(join(__dirname, file))
  console.log(`  ${exists ? '✅' : '❌'} ${file}`)
  if (!exists) allFilesExist = false
}

if (!allFilesExist) {
  console.log('\n❌ 关键文件缺失，请先运行构建命令: npm run build')
  process.exit(1)
}

// 2. 检查 Worker 脚本内容
console.log('\n🔧 检查 Worker 脚本:')
try {
  const workerPath = join(__dirname, 'packages/main/dist/upload-worker.js')
  const { readFileSync } = await import('fs')
  const workerContent = readFileSync(workerPath, 'utf8')
  
  // 检查关键导入
  const hasWorkerThreads = workerContent.includes('worker_threads')
  const hasAliOss = workerContent.includes('ali-oss')
  const hasParentPort = workerContent.includes('parentPort')
  
  console.log(`  ${hasWorkerThreads ? '✅' : '❌'} worker_threads 导入`)
  console.log(`  ${hasAliOss ? '✅' : '❌'} ali-oss 导入`)
  console.log(`  ${hasParentPort ? '✅' : '❌'} parentPort 使用`)
  
  if (!hasWorkerThreads || !hasParentPort) {
    console.log('  ⚠️  Worker 脚本可能有问题')
  }
} catch (error) {
  console.log(`  ❌ 无法读取 Worker 脚本: ${error.message}`)
}

// 3. 检查依赖项
console.log('\n📦 检查依赖项:')
try {
  const packageJson = JSON.parse(
    await import('fs').then(fs => fs.readFileSync(join(__dirname, 'package.json'), 'utf8'))
  )
  
  const requiredDeps = ['ali-oss', 'better-sqlite3', 'electron']
  for (const dep of requiredDeps) {
    const exists = packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]
    console.log(`  ${exists ? '✅' : '❌'} ${dep}`)
  }
} catch (error) {
  console.log(`  ❌ 无法检查依赖项: ${error.message}`)
}

// 4. 尝试启动应用并捕获输出
console.log('\n🚀 尝试启动应用 (10秒超时):')
console.log('   如果应用正常启动，您应该看到窗口出现')
console.log('   如果失败，将显示错误信息\n')

const electronPath = join(__dirname, 'node_modules/.bin/electron.cmd')
const entryPoint = join(__dirname, 'packages/entry-point.mjs')

const child = spawn(electronPath, [entryPoint], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: {
    ...process.env,
    NODE_ENV: 'production',
    ELECTRON_ENABLE_LOGGING: '1'
  }
})

let output = ''
let errorOutput = ''

child.stdout.on('data', (data) => {
  const text = data.toString()
  output += text
  console.log('📤 STDOUT:', text.trim())
})

child.stderr.on('data', (data) => {
  const text = data.toString()
  errorOutput += text
  console.log('📥 STDERR:', text.trim())
})

child.on('exit', (code, signal) => {
  console.log(`\n🏁 应用退出: code=${code}, signal=${signal}`)
  
  if (code === 0) {
    console.log('✅ 应用正常退出')
  } else {
    console.log('❌ 应用异常退出')
    
    // 分析常见错误
    if (errorOutput.includes('worker_threads')) {
      console.log('💡 可能的问题: Worker Threads 相关错误')
      console.log('   建议: 检查 Node.js 版本是否支持 worker_threads')
    }
    
    if (errorOutput.includes('ali-oss')) {
      console.log('💡 可能的问题: ali-oss 依赖问题')
      console.log('   建议: 运行 npm install 重新安装依赖')
    }
    
    if (errorOutput.includes('Cannot find module')) {
      console.log('💡 可能的问题: 模块缺失')
      console.log('   建议: 检查构建输出和依赖安装')
    }
    
    if (output.includes('Worker') || errorOutput.includes('Worker')) {
      console.log('💡 可能的问题: Worker 初始化失败')
      console.log('   建议: 检查 Worker 脚本路径和权限')
    }
  }
})

child.on('error', (error) => {
  console.log(`❌ 启动失败: ${error.message}`)
})

// 10秒后强制终止
setTimeout(() => {
  if (!child.killed) {
    console.log('\n⏰ 10秒超时，强制终止进程')
    child.kill('SIGTERM')
    
    setTimeout(() => {
      if (!child.killed) {
        child.kill('SIGKILL')
      }
    }, 2000)
  }
}, 10000)

console.log('⏳ 等待应用启动...')
