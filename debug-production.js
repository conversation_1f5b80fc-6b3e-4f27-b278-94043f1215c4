#!/usr/bin/env node

/**
 * 生产环境调试脚本 - 用于诊断打包后的应用启动问题
 */

import { spawn } from 'child_process'
import { join, dirname } from 'path'
import { fileURLToPath } from 'url'
import { existsSync, readdirSync, statSync } from 'fs'

const __dirname = dirname(fileURLToPath(import.meta.url))

console.log('🔍 诊断生产环境应用启动问题...\n')

// 1. 检查构建输出
console.log('📁 检查构建输出:')
const distDir = join(__dirname, 'dist')
if (!existsSync(distDir)) {
  console.log('❌ dist 目录不存在，请先运行: npm run package')
  process.exit(1)
}

// 查找可执行文件
function findExecutable() {
  const files = readdirSync(distDir)
  for (const file of files) {
    const filePath = join(distDir, file)
    if (statSync(filePath).isDirectory()) {
      // 检查是否是应用目录
      const possibleExe = [
        join(filePath, 'clipnest.exe'),
        join(filePath, 'clipnest'),
        join(filePath, 'ClipNest.exe'),
        join(filePath, 'ClipNest')
      ]
      
      for (const exe of possibleExe) {
        if (existsSync(exe)) {
          return exe
        }
      }
      
      // 检查子目录
      try {
        const subFiles = readdirSync(filePath)
        for (const subFile of subFiles) {
          if (subFile.endsWith('.exe') || (!subFile.includes('.') && statSync(join(filePath, subFile)).isFile())) {
            const exePath = join(filePath, subFile)
            if (existsSync(exePath)) {
              return exePath
            }
          }
        }
      } catch (error) {
        // 忽略权限错误
      }
    }
  }
  return null
}

const executablePath = findExecutable()
if (!executablePath) {
  console.log('❌ 未找到可执行文件')
  console.log('   请检查 dist 目录内容:')
  try {
    const files = readdirSync(distDir)
    files.forEach(file => {
      const filePath = join(distDir, file)
      const isDir = statSync(filePath).isDirectory()
      console.log(`   ${isDir ? '📁' : '📄'} ${file}`)
      
      if (isDir) {
        try {
          const subFiles = readdirSync(filePath)
          subFiles.slice(0, 5).forEach(subFile => {
            console.log(`      📄 ${subFile}`)
          })
          if (subFiles.length > 5) {
            console.log(`      ... 还有 ${subFiles.length - 5} 个文件`)
          }
        } catch (error) {
          console.log(`      ❌ 无法读取目录: ${error.message}`)
        }
      }
    })
  } catch (error) {
    console.log(`   ❌ 无法读取 dist 目录: ${error.message}`)
  }
  process.exit(1)
}

console.log(`✅ 找到可执行文件: ${executablePath}`)

// 2. 检查应用内部结构
console.log('\n🔍 检查应用内部结构:')
const appDir = dirname(executablePath)
const resourcesDir = join(appDir, 'resources')

if (existsSync(resourcesDir)) {
  console.log('✅ resources 目录存在')
  
  // 检查 app.asar 或 app 目录
  const appAsar = join(resourcesDir, 'app.asar')
  const appFolder = join(resourcesDir, 'app')
  
  if (existsSync(appAsar)) {
    console.log('✅ app.asar 存在')
    console.log('   注意: asar 打包的应用需要特殊处理 Worker 路径')
  } else if (existsSync(appFolder)) {
    console.log('✅ app 目录存在')
    
    // 检查关键文件
    const keyFiles = [
      'packages/entry-point.mjs',
      'node_modules/@app/main/dist/index.js',
      'node_modules/@app/main/dist/upload-worker.js'
    ]
    
    for (const file of keyFiles) {
      const filePath = join(appFolder, file)
      const exists = existsSync(filePath)
      console.log(`   ${exists ? '✅' : '❌'} ${file}`)
    }
  } else {
    console.log('❌ 未找到 app.asar 或 app 目录')
  }
} else {
  console.log('❌ resources 目录不存在')
}

// 3. 尝试启动应用
console.log('\n🚀 尝试启动应用 (15秒超时):')
console.log('   监听应用输出以诊断问题...\n')

const child = spawn(executablePath, [], {
  stdio: ['pipe', 'pipe', 'pipe'],
  env: {
    ...process.env,
    NODE_ENV: 'production',
    ELECTRON_ENABLE_LOGGING: '1',
    ELECTRON_LOG_ASAR_READS: '1',
    DEBUG: '*'
  }
})

let hasOutput = false
let outputBuffer = ''
let errorBuffer = ''

child.stdout.on('data', (data) => {
  hasOutput = true
  const text = data.toString()
  outputBuffer += text
  console.log('📤 STDOUT:', text.trim())
})

child.stderr.on('data', (data) => {
  hasOutput = true
  const text = data.toString()
  errorBuffer += text
  console.log('📥 STDERR:', text.trim())
})

child.on('exit', (code, signal) => {
  console.log(`\n🏁 应用退出: code=${code}, signal=${signal}`)
  
  if (!hasOutput) {
    console.log('⚠️  应用没有产生任何输出，可能是静默崩溃')
    console.log('💡 建议检查:')
    console.log('   1. 应用是否有足够的权限')
    console.log('   2. 依赖的 DLL 文件是否存在')
    console.log('   3. Windows 事件查看器中的应用程序日志')
  }
  
  // 分析错误
  const allOutput = outputBuffer + errorBuffer
  
  if (allOutput.includes('worker_threads')) {
    console.log('💡 Worker Threads 相关问题')
    console.log('   可能原因: Node.js 版本不兼容或 Worker 脚本路径错误')
  }
  
  if (allOutput.includes('upload-worker')) {
    console.log('💡 Upload Worker 相关问题')
    console.log('   可能原因: Worker 脚本文件缺失或路径解析错误')
  }
  
  if (allOutput.includes('Cannot find module')) {
    console.log('💡 模块缺失问题')
    console.log('   可能原因: 依赖模块未正确打包')
  }
  
  if (allOutput.includes('ali-oss')) {
    console.log('💡 ali-oss 依赖问题')
    console.log('   可能原因: ali-oss 模块未正确打包到 Worker 中')
  }
  
  if (code !== 0) {
    console.log('\n❌ 应用异常退出')
    console.log('💡 建议操作:')
    console.log('   1. 检查 Windows 事件查看器')
    console.log('   2. 尝试在命令行中直接运行应用')
    console.log('   3. 检查应用依赖和权限')
  }
  
  process.exit(code || 0)
})

child.on('error', (error) => {
  console.log(`❌ 启动失败: ${error.message}`)
  
  if (error.code === 'ENOENT') {
    console.log('💡 可执行文件不存在或无法访问')
  } else if (error.code === 'EACCES') {
    console.log('💡 权限不足，尝试以管理员身份运行')
  }
  
  process.exit(1)
})

// 15秒后强制终止
setTimeout(() => {
  if (!child.killed) {
    console.log('\n⏰ 15秒超时，强制终止进程')
    child.kill('SIGTERM')
    
    setTimeout(() => {
      if (!child.killed) {
        child.kill('SIGKILL')
      }
    }, 3000)
  }
}, 15000)

console.log('⏳ 等待应用启动...')
