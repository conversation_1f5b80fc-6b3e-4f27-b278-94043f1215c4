import React, { useState, useRef, useCallback, useEffect } from 'react'
import { cn } from '@/components/lib/utils'
import { FolderPlus, UploadIcon, ChevronDown } from 'lucide-react'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Button } from '@/components/ui/button'
import { ResourceSource } from '@/types/resources'
import { useUploadTasks } from '@/hooks/useUploadTasks'
import { TokenManager, TeamManager } from '@/libs/storage'
import { UploadTask } from '@app/shared/types/database.types'

// 简化的上传文件接口，保持与原组件兼容
interface UploadedFile {
  id: string
  file: File
  status: 'uploading' | 'success' | 'error'
  error?: string
  url?: string
  fileName?: string
  progress?: number
  objectId?: string
  fileMd5?: string
  folderUuid: string
}

interface FolderUploadedFile extends UploadedFile {
  relativePath: string
}

interface UnifiedUploaderProps {
  folderUuid: string
  orientation?: 'horizontal' | 'vertical' // 用于card模式控制布局
  mode?: 'card' | 'popover' // 选择展示模式
  resourceType?: ResourceSource
  fileUploadTypes?: string[]
  onUpload?: (uploaded: (FolderUploadedFile | UploadedFile)[]) => void
  buttonLabel?: string // 弹出菜单按钮文字
  customFileUploaderRender?: (props: any) => React.ReactNode // 保持兼容性，但不使用
}

// 文件类型检查工具函数
const isValidFileType = (file: File, allowedTypes?: string[]): boolean => {
  if (!allowedTypes || allowedTypes.length === 0) return true

  return allowedTypes.some(type => {
    if (type === '*/*') return true
    if (type.endsWith('/*')) {
      const category = type.slice(0, -2)
      return file.type.startsWith(category + '/')
    }
    return file.type === type
  })
}

// 获取文件类型枚举
const getFileType = (file: File): UploadTask.Type => {
  if (file.type.startsWith('video/')) return UploadTask.Type.VIDEO
  if (file.type.startsWith('audio/')) return UploadTask.Type.AUDIO
  if (file.type.startsWith('image/')) return UploadTask.Type.IMAGE
  return UploadTask.Type.OTHER
}

const UploadMaterial: React.FC<UnifiedUploaderProps> = ({
  folderUuid,
  orientation = 'horizontal',
  mode = 'popover',
  resourceType = ResourceSource.MEDIA, // 保持兼容性
  fileUploadTypes,
  onUpload,
  buttonLabel = '上传',
  customFileUploaderRender, // 保持兼容性，但不使用
}) => {
  // 避免未使用变量警告
  void resourceType
  void customFileUploaderRender
  const [isUploading, setIsUploading] = useState<boolean>(false)
  const [hovered, setHovered] = useState(false)
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [currentTaskIds, setCurrentTaskIds] = useState<number[]>([])

  // 文件输入引用
  const fileInputRef = useRef<HTMLInputElement>(null)
  const folderInputRef = useRef<HTMLInputElement>(null)

  // 使用上传任务 hook
  const {
    tasks,
    createTask,
    uploadFileContent
  } = useUploadTasks()

  const currentUser = TokenManager.getUserId()
  const currentTeam = TeamManager.current()

  // 监控当前上传任务的进度
  useEffect(() => {
    if (currentTaskIds.length === 0) {
      setUploadProgress(0)
      return
    }

    const currentTasks = tasks.filter(task => currentTaskIds.includes(task.id))
    if (currentTasks.length === 0) return

    const totalProgress = currentTasks.reduce((sum, task) => sum + (task.progress || 0), 0)
    const avgProgress = totalProgress / currentTasks.length
    setUploadProgress(Math.round(avgProgress * 100))

    // 检查是否所有任务都完成
    const allCompleted = currentTasks.every(task =>
      task.status === UploadTask.Status.COMPLETED ||
      task.status === UploadTask.Status.FAILED ||
      task.status === UploadTask.Status.CANCELLED
    )

    if (allCompleted) {
      setIsUploading(false)
      setCurrentTaskIds([])
      setUploadProgress(0)
    }
  }, [tasks, currentTaskIds])

  // 处理文件选择
  const handleFileSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return
    if (!currentUser) {
      console.error('请先登录')
      return
    }

    setIsUploading(true)
    const uploadResults: UploadedFile[] = []
    const taskIds: number[] = []

    try {
      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // 检查文件类型
        if (!isValidFileType(file, fileUploadTypes)) {
          console.error(`文件 ${file.name} 类型不支持`)
          continue
        }

        // 创建上传任务
        const task = await createTask({
          name: file.name,
          local_path: '', // 浏览器环境下没有本地路径
          type: getFileType(file),
          folder_id: folderUuid,
          upload_module: 'media',
          uid: String(currentUser),
          team_id: currentTeam || 0
        })

        const taskId = task.id
        taskIds.push(taskId)

        // 上传文件内容
        const arrayBuffer = await file.arrayBuffer()
        const result = await uploadFileContent({
          taskId,
          fileContent: arrayBuffer,
          fileName: file.name
        })

        const uploadedFile: UploadedFile = {
          id: String(taskId),
          file,
          status: result.success ? 'success' : 'error',
          error: result.error,
          url: result.url,
          fileName: file.name,
          progress: result.success ? 1 : 0,
          folderUuid
        }

        uploadResults.push(uploadedFile)
      }

      // 设置当前任务ID用于进度监控
      setCurrentTaskIds(taskIds)

      // 调用回调
      onUpload?.(uploadResults)

    } catch (error) {
      console.error('文件上传失败:', error)
      setIsUploading(false)
    }
  }, [fileUploadTypes, folderUuid, currentUser, currentTeam, createTask, uploadFileContent, onUpload])

  // 处理文件夹选择
  const handleFolderSelect = useCallback(async (files: FileList | null) => {
    if (!files || files.length === 0) return

    // 将 FileList 转换为带有相对路径的文件数组
    const folderFiles: FolderUploadedFile[] = []
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      const webkitRelativePath = (file as any).webkitRelativePath || file.name

      folderFiles.push({
        id: `folder-${i}-${Date.now()}`,
        file,
        relativePath: webkitRelativePath,
        status: 'uploading',
        folderUuid,
        fileName: file.name,
        progress: 0
      })
    }

    await handleFileSelect(files)
  }, [handleFileSelect, folderUuid])

  // 触发文件选择
  const triggerFileSelect = useCallback(() => {
    fileInputRef.current?.click()
  }, [])

  // 触发文件夹选择
  const triggerFolderSelect = useCallback(() => {
    folderInputRef.current?.click()
  }, [])

  // 隐藏的文件输入元素
  const renderHiddenInputs = () => (
    <>
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={fileUploadTypes?.join(',')}
        style={{ display: 'none' }}
        onChange={(e) => handleFileSelect(e.target.files)}
      />
      <input
        ref={folderInputRef}
        type="file"
        {...({ webkitdirectory: true } as any)}
        multiple
        style={{ display: 'none' }}
        onChange={(e) => handleFolderSelect(e.target.files)}
      />
    </>
  )

  if (mode === 'card') {
    return (
      <div
        className={cn(
          'relative flex items-center justify-center border-dashed border-2 border-gray-200 rounded-lg text-sm cursor-pointer overflow-hidden transition-all duration-300',
          orientation === 'horizontal' ? 'w-50 h-50' : 'w-40 h-64',
        )}
        onMouseEnter={() => setHovered(true)}
        onMouseLeave={() => setHovered(false)}
      >
        {renderHiddenInputs()}

        <div
          className={cn(
            'flex flex-col items-center justify-center transition-opacity duration-300',
            hovered ? 'opacity-0 scale-95' : 'opacity-100 scale-100',
          )}
        >
          <span className="text-3xl text-gray-400 mb-2">+</span>
          <span className="text-gray-500">上传素材</span>
        </div>

        {isUploading && (
          <div className="absolute bottom-0 left-1 text-xs text-blue-600">
            上传中... {uploadProgress > 0 && `${uploadProgress}%`}
          </div>
        )}

        <div
          className={cn(
            'absolute inset-0 text-gray-500 flex flex-col transition-all duration-300',
            hovered ? 'opacity-100 scale-y-100' : 'opacity-0 scale-y-0 pointer-events-none',
            'origin-center',
          )}
        >
          <button
            className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors"
            onClick={triggerFileSelect}
            disabled={isUploading}
          >
            <UploadIcon className="w-5 h-5" />
            上传文件
          </button>

          <button
            className="flex items-center justify-center gap-2 flex-1 hover:text-primary-highlight1 transition-colors"
            onClick={triggerFolderSelect}
            disabled={isUploading}
          >
            <FolderPlus className="w-5 h-5" />
            文件夹上传
          </button>
        </div>

        <div
          className={cn(
            'absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full border-b-2 border-dashed border-gray-200',
            hovered ? 'opacity-100' : 'opacity-0',
          )}
        />
      </div>
    )
  } else {
    // 弹出菜单模式
    return (
      <div>
        {renderHiddenInputs()}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className="w-36 border-0 bg-primary/10 flex justify-between gap-1"
              disabled={isUploading}
            >
              <span>
                {buttonLabel}
                {isUploading && <span> (上传中... {uploadProgress > 0 && `${uploadProgress}%`})</span>}
              </span>
              <ChevronDown className="w-3.5 h-3.5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-36 p-0">
            <div className="py-1">
              <button
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2"
                onClick={triggerFileSelect}
                disabled={isUploading}
              >
                <UploadIcon className="w-3.5 h-3.5" />
                上传文件
              </button>

              <button
                className="w-full text-left px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-800 flex items-center gap-2"
                onClick={triggerFolderSelect}
                disabled={isUploading}
              >
                <FolderPlus className="w-3.5 h-3.5" />
                文件夹上传
              </button>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    )
  }
}
export default UploadMaterial
