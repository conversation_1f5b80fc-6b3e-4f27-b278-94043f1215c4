import dotenv from 'dotenv'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

import { initApp } from '@app/main'

const __dirname = dirname(fileURLToPath(import.meta.url))

dotenv.config({ path: join(__dirname, '../.env') })

// 全局错误处理
function handleError(error, source = 'unknown') {
  console.error(`[${source}] 应用错误:`, error)

  // 分析错误类型
  if (error.message?.includes('worker_threads')) {
    console.error('💡 Worker Threads 相关错误，可能是 Node.js 版本问题或模块缺失')
  }

  if (error.message?.includes('ali-oss')) {
    console.error('💡 ali-oss 依赖问题，请检查依赖安装')
  }

  if (error.code === 'MODULE_NOT_FOUND') {
    console.error('💡 模块未找到，请检查构建输出和依赖安装')
  }

  // 在生产环境中也显示错误，便于调试
  process.exit(1)
}

process.on('uncaughtException', (error) => handleError(error, 'uncaughtException'))
process.on('unhandledRejection', (error) => handleError(error, 'unhandledRejection'))

// noinspection JSIgnoredPromiseFromCall
/**
 * We resolve '@app/renderer' and '@app/preload'
 * here and not in '@app/main'
 * to observe good practices of modular design.
 * This allows fewer dependencies and better separation of concerns in '@app/main'.
 * Thus,
 * the main module remains simplistic and efficient
 * as it receives initialization instructions rather than direct module imports.
 */

// 启动应用并处理错误
initApp(
  {
    renderer: (process.env.MODE === 'development' && !!process.env.VITE_DEV_SERVER_URL)
      ? new URL(process.env.VITE_DEV_SERVER_URL)
      : {
        path: fileURLToPath(import.meta.resolve('@app/renderer')),
      },

    preload: {
      path: fileURLToPath(import.meta.resolve('@app/preload/exposed.mjs')),
    },
  },
).catch(error => {
  console.error('❌ 应用启动失败:', error)
  handleError(error, 'initApp')
})
