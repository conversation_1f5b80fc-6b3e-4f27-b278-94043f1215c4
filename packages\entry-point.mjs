import dotenv from 'dotenv'
import { join, dirname } from 'node:path'
import { fileURLToPath } from 'node:url'

import { initApp } from '@app/main'

const __dirname = dirname(fileURLToPath(import.meta.url))

dotenv.config({ path: join(__dirname, '../.env') })

// 全局错误处理
function handleError(error, source = 'unknown') {
  console.error(`[${source}] 应用错误:`, error)

  // 分析错误类型并提供有用的诊断信息
  if (error.message?.includes('ENOTDIR')) {
    console.error('💡 ENOTDIR 错误: 文件路径问题，可能是数据库或迁移文件路径配置错误')
  }

  if (error.message?.includes('worker_threads')) {
    console.error('💡 Worker Threads 相关错误，可能是 Node.js 版本问题或 Worker 脚本路径错误')
  }

  if (error.message?.includes('ali-oss')) {
    console.error('💡 ali-oss 依赖问题，请检查依赖安装和打包')
  }

  if (error.code === 'MODULE_NOT_FOUND') {
    console.error('💡 模块未找到，请检查构建输出和依赖安装')
  }

  if (error.message?.includes('数据库初始化失败')) {
    console.error('💡 数据库初始化失败，可能是路径或权限问题')
  }

  // 在生产环境中也显示错误，便于调试
  process.exit(1)
}

// 注册全局错误处理器
process.on('uncaughtException', error => handleError(error, 'uncaughtException'))
process.on('unhandledRejection', error => handleError(error, 'unhandledRejection'))

// noinspection JSIgnoredPromiseFromCall
/**
 * We resolve '@app/renderer' and '@app/preload'
 * here and not in '@app/main'
 * to observe good practices of modular design.
 * This allows fewer dependencies and better separation of concerns in '@app/main'.
 * Thus,
 * the main module remains simplistic and efficient
 * as it receives initialization instructions rather than direct module imports.
 */

// 启动应用并处理错误
initApp(
  {
    renderer: (process.env.MODE === 'development' && !!process.env.VITE_DEV_SERVER_URL)
      ? new URL(process.env.VITE_DEV_SERVER_URL)
      : {
        path: fileURLToPath(import.meta.resolve('@app/renderer')),
      },

    preload: {
      path: fileURLToPath(import.meta.resolve('@app/preload/exposed.mjs')),
    },
  },
).catch(error => {
  console.error('❌ 应用启动失败:', error)
  handleError(error, 'initApp')
})
