import { forwardRef, Inject, Injectable, Logger, OnApplicationShutdown, OnModuleInit } from '@nestjs/common'
import { BrowserWindow, app } from 'electron'
import { Worker } from 'worker_threads'
import { dirname, join } from 'path'
import { fileURLToPath } from 'url'
import { cpus } from 'os'
import { existsSync } from 'fs'
import { UploadTaskModel } from '@/infra/models/UploadTaskModel.js'
import { UploadTaskService } from './upload-task.service.js'
import { UploadTask } from '@app/shared/types/database.types.js'
import { FileUploaderService } from '../../file-uploader/file-uploader.service.js'

/**
 * Worker任务数据接口
 */
interface WorkerTaskData {
  taskId: number
  filePath: string
  fileName: string
  uploadConfig: {
    folderUuid?: string
    fileMd5?: string
    module: string
    partSize: number
    checkpointData?: string  // 断点续传数据
  }
}

/**
 * 上传队列管理器 - 基于Worker进程的轻量级任务协调器
 * 负责创建和管理Worker进程，处理任务分发
 */
@Injectable()
export class UploadQueueManager implements OnApplicationShutdown, OnModuleInit {

  private readonly logger = new Logger(UploadQueueManager.name)

  //#region 内部状态
  /**
   * Worker池
   */
  private workers: Map<string, Worker> = new Map()

  /**
   * 任务到Worker的映射
   */
  private taskWorkerMap: Map<number, string> = new Map()

  /**
   * Worker最后使用时间跟踪
   */
  private workerLastUsed: Map<string, number> = new Map()

  /**
   * Worker当前任务数跟踪
   */
  private workerTaskCount: Map<string, number> = new Map()

  /**
   * 队列处理器定时器
   */
  private queueProcessorTimer?: NodeJS.Timeout

  /**
   * Worker清理定时器
   */
  private workerCleanupTimer?: NodeJS.Timeout

  /**
   * 是否已初始化
   */
  private initialized = false

  /**
   * 最大Worker数量
   */
  private readonly MAX_WORKERS = Math.min(cpus().length, 4)

  /**
   * Worker空闲超时时间（5分钟）
   */
  private readonly WORKER_IDLE_TIMEOUT = 5 * 60 * 1000

  /**
   * Worker清理检查间隔（1分钟）
   */
  private readonly CLEANUP_INTERVAL = 60 * 1000

  /**
   * 进度事件批量发送的最大批量大小
   */
  private readonly MAX_BATCH_SIZE = 100

  /**
   * 进度事件批量发送的异常大小阈值（用于警告）
   */
  private readonly ABNORMAL_BATCH_SIZE = 500

  /**
   * Worker脚本路径
   */
  private readonly workerScriptPath = this.getWorkerScriptPath()

  /**
   * 进度事件批量发送
   */
  private progressEventBatch: UploadTask.ProgressEvent[] = []
  private progressBatchTimer?: NodeJS.Timeout
  //#endregion

  onModuleInit(): any {
    try {
      // 验证 Worker 脚本路径是否存在
      this.validateWorkerScript()
      this.logger.debug(`Worker 脚本路径验证成功: ${this.workerScriptPath}`)
    } catch (error) {
      this.logger.error(`Worker 脚本路径验证失败: ${error}`)
      throw error
    }

    // 设置队列管理器引用到服务中
    this.uploadTaskService.setQueueManager(this)

    // 延迟启动队列处理器，确保所有服务都已初始化
    this.scheduleQueueStart()

    // 启动Worker清理定时器
    this.startWorkerCleanup()
  }

  constructor(
    @Inject(forwardRef(() => UploadTaskService)) private readonly uploadTaskService: UploadTaskService,
    @Inject(FileUploaderService) private readonly fileUploaderService: FileUploaderService
  ) {
  }

  /**
   * 调度队列启动，等待数据库准备就绪
   */
  private scheduleQueueStart(): void {
    const checkAndStart = () => {
      try {
        // 尝试启动队列处理器，如果数据库未准备好会抛出异常
        this.startQueueProcessor()
      } catch (error) {
        // 数据库还未准备好，继续等待
        setTimeout(checkAndStart, 2000)
      }
    }

    // 延迟开始检查
    setTimeout(checkAndStart, 5000)
  }

  /**
   * 启动队列处理器
   */
  startQueueProcessor(): void {
    if (this.initialized) {
      console.log('[UploadQueueManager] 队列处理器已经启动')
      return
    }

    console.log('[UploadQueueManager] 启动队列处理器')

    // 重置所有上传中的任务为等待状态
    // this.uploadTaskService.resetUploadingTasks()

    // 每3秒检查一次队列
    this.queueProcessorTimer = setInterval(() => {
      this.processQueue()
    }, 3000)

    this.initialized = true

    // 立即处理一次
    this.processQueue()
  }

  /**
   * 停止队列处理器
   */
  stopQueueProcessor(): void {
    if (this.queueProcessorTimer) {
      clearInterval(this.queueProcessorTimer)
      this.queueProcessorTimer = undefined
    }

    // 清理所有Worker
    for (const worker of this.workers.values()) {
      worker.terminate()
    }
    this.workers.clear()
    this.taskWorkerMap.clear()

    this.initialized = false
  }

  /**
   * 处理上传队列
   */
  private async processQueue(): Promise<void> {
    try {
      const config = this.uploadTaskService.getQueueConfig()
      const currentActiveCount = this.taskWorkerMap.size

      if (currentActiveCount >= config.max_concurrent_uploads) {
        return
      }

      const pendingTasks = this.uploadTaskService.getTasksByStatus(UploadTask.Status.PENDING)
      const availableSlots = config.max_concurrent_uploads - currentActiveCount

      const tasksToStart = pendingTasks.slice(0, availableSlots)

      for (const task of tasksToStart) {
        await this.startUpload(task)
      }
    } catch (error) {
      // 如果数据库尚未初始化，静默返回等待下次检查
      if (error instanceof Error && error.message.includes('数据库尚未初始化')) {
        return
      }
      console.error('[UploadQueueManager] 处理队列时出错:', error)
    }
  }

  /**
   * 开始上传任务
   */
  private async startUpload(task: UploadTaskModel): Promise<void> {
    try {
      if (this.taskWorkerMap.has(task.id)) {
        console.log(`[UploadQueueManager] 任务 ${task.id} 已在上传中`)
        return
      }

      // 更新任务状态为上传中
      this.uploadTaskService.updateTaskStatus(task.id, UploadTask.Status.UPLOADING)

      // 创建或获取Worker
      const workerId = await this.createWorker()
      const worker = this.workers.get(workerId)!

      // 记录任务到Worker的映射
      this.taskWorkerMap.set(task.id, workerId)

      // 增加Worker任务计数
      const currentCount = this.workerTaskCount.get(workerId) || 0
      this.workerTaskCount.set(workerId, currentCount + 1)

      // 准备上传数据
      const taskData: WorkerTaskData = {
        taskId: task.id,
        filePath: task.local_path,
        fileName: task.name,
        uploadConfig: {
          folderUuid: task.folder_id || undefined,
          fileMd5: task.hash || undefined,
          module: task.upload_module,
          partSize: 1024 * 1024, // 1MB
          checkpointData: task.checkpoint_data || undefined  // 传递断点续传数据
        }
      }

      // 发送上传任务到Worker
      worker.postMessage({
        type: 'upload',
        data: taskData
      })

      console.log(`[UploadQueueManager] 任务 ${task.id} 已分配给Worker ${workerId}`)
    } catch (error) {
      console.error(`[UploadQueueManager] 开始上传任务 ${task.id} 时出错:`, error)
      this.uploadTaskService.updateTaskStatus(task.id, UploadTask.Status.FAILED, error instanceof Error ? error.message : '上传失败')
      this.taskWorkerMap.delete(task.id)
    }
  }

  /**
   * 创建或获取Worker
   */
  private async createWorker(): Promise<string> {
    // 首先尝试找到空闲的Worker（任务数为0的Worker）
    const idleWorkerId = this.findIdleWorker()
    if (idleWorkerId) {
      this.updateWorkerUsage(idleWorkerId)
      console.log(`[UploadQueueManager] 复用空闲Worker ${idleWorkerId}`)
      return idleWorkerId
    }

    // 如果没有空闲Worker且未达到最大数量，创建新Worker
    if (this.workers.size < this.MAX_WORKERS) {
      const workerId = this.generateWorkerId()

      try {
        const worker = new Worker(this.workerScriptPath)

        // 设置Worker消息处理
        worker.on('message', message => {
          this.handleWorkerMessage(workerId, message)
        })

        worker.on('error', error => {
          console.error(`[UploadQueueManager] Worker ${workerId} 错误:`, error)
          this.cleanupWorker(workerId)
        })

        worker.on('exit', code => {
          console.log(`[UploadQueueManager] Worker ${workerId} 退出，代码: ${code}`)
          this.cleanupWorker(workerId)
        })

        // 初始化Worker状态
        this.workers.set(workerId, worker)
        this.workerTaskCount.set(workerId, 0)
        this.updateWorkerUsage(workerId)

        console.log(`[UploadQueueManager] 创建新Worker ${workerId} (总数: ${this.workers.size}/${this.MAX_WORKERS})`)
        return workerId
      } catch (error) {
        console.error('[UploadQueueManager] 创建Worker失败:', error)
        throw new Error(`无法创建Worker: ${error instanceof Error ? error.message : '未知错误'}`)
      }
    }

    // 如果达到最大Worker数，选择任务数最少的Worker
    const leastBusyWorkerId = this.findLeastBusyWorker()
    if (!leastBusyWorkerId) {
      throw new Error('无法获取可用的Worker')
    }

    this.updateWorkerUsage(leastBusyWorkerId)
    console.log(`[UploadQueueManager] 分配给最空闲Worker ${leastBusyWorkerId}`)
    return leastBusyWorkerId
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(workerId: string, message: any): void {
    const { type, taskId, data } = message

    switch (type) {
      case 'progress':
        this.handleProgressUpdate(taskId, data.progress, data.checkpoint)
        break

      case 'complete':
        this.handleUploadComplete(taskId, data)
        break

      case 'error':
        this.handleUploadError(taskId, data.error)
        break

      case 'paused':
        this.handleUploadPaused(taskId, data.checkpoint)
        break

      case 'resumed':
        this.handleUploadResumed(taskId, data.checkpoint)
        break

      case 'request-oss-signature':
        this.handleOSSSignatureRequest(workerId, message.requestId, data)
        break

      default:
        console.warn(`[UploadQueueManager] 未知的Worker消息类型: ${type}`)
    }
  }

  /**
   * 处理OSS签名请求
   */
  private async handleOSSSignatureRequest(workerId: string, requestId: string, data: any): Promise<void> {
    try {
      const signature = await this.getOSSSignature(data)
      const worker = this.workers.get(workerId)
      if (worker) {
        worker.postMessage({
          type: 'oss-signature-response',
          requestId,
          success: true,
          data: signature
        })
      }
    } catch (error) {
      console.error('[UploadQueueManager] 获取OSS签名失败:', error)
      const worker = this.workers.get(workerId)
      if (worker) {
        worker.postMessage({
          type: 'oss-signature-response',
          requestId,
          success: false,
          error: error instanceof Error ? error.message : '获取OSS签名失败'
        })
      }
    }
  }

  /**
   * 获取OSS签名 - 复用FileUploaderService的逻辑
   */
  private async getOSSSignature(params: any): Promise<any> {
    // 使用FileUploaderService的generateSTSUrl方法
    const signature = await (this.fileUploaderService as any).generateSTSUrl(
      params.fileName,
      params.folderUuid || '',
      params.fileMd5,
      params.module
    )

    if (signature instanceof Error) {
      throw signature
    }

    return signature
  }

  /**
   * 处理进度更新
   */
  private handleProgressUpdate(taskId: number, progress: number, checkpoint?: any): void {
    console.log(`[UploadQueueManager] 接收到进度更新: 任务${taskId}, 进度${(progress * 100).toFixed(1)}%`)

    // 获取当前任务状态
    const task = this.uploadTaskService.findById(taskId)
    if (task && task.status === UploadTask.Status.PENDING && progress > 0) {
      // 如果任务状态还是 PENDING 但已经有进度，更新状态为 UPLOADING
      console.log(`[UploadQueueManager] 任务${taskId} 状态从 PENDING 更新为 UPLOADING`)
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.UPLOADING)
    }

    // 更新数据库中的进度
    this.uploadTaskService.update(taskId, {
      progress: progress,
      ...(checkpoint ? { checkpoint_data: JSON.stringify(checkpoint) } : {})
    })

    // 检查批量数组是否异常大，记录警告
    if (this.progressEventBatch.length >= this.ABNORMAL_BATCH_SIZE) {
      console.warn(`[UploadQueueManager] 进度事件批量数组异常大: ${this.progressEventBatch.length} 个事件，可能存在内存泄漏`)
    }

    const existingEventIndex = this.progressEventBatch.findIndex(event => event.task_id === taskId)
    if (existingEventIndex !== -1) {
      this.progressEventBatch[existingEventIndex].progress = progress
    } else {
      this.progressEventBatch.push({
        task_id: taskId,
        progress: progress
      })
    }

    // 如果达到最大批量大小，立即发送，不等待定时器
    if (this.progressEventBatch.length >= this.MAX_BATCH_SIZE) {
      this.sendBatchProgressEvents()
      return
    }

    // 如果还没有批量发送定时器，创建一个作为兜底机制
    if (!this.progressBatchTimer) {
      this.progressBatchTimer = setTimeout(() => {
        this.sendBatchProgressEvents()
      }, 500)
    }
  }

  /**
   * 批量发送进度事件
   */
  private sendBatchProgressEvents(): void {
    // 清理定时器
    if (this.progressBatchTimer) {
      clearTimeout(this.progressBatchTimer)
      this.progressBatchTimer = undefined
    }

    const batchSize = this.progressEventBatch.length
    if (batchSize === 0) {
      return
    }

    // 检查是否为异常大的批量，记录警告
    if (batchSize >= this.ABNORMAL_BATCH_SIZE) {
      console.warn(`[UploadQueueManager] 发送异常大的进度事件批量: ${batchSize} 个事件`)
    }

    try {
      // 创建事件副本，避免在发送过程中被修改
      const eventsToSend = [...this.progressEventBatch]

      // 立即清空批量数组，确保即使发送失败也不会导致内存泄漏
      this.progressEventBatch = []

      // 发送到所有窗口
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(window => {
        try {
          window.webContents.send('batch-upload-progress', eventsToSend)
        } catch (error) {
          console.error('[UploadQueueManager] 向窗口发送进度事件失败:', error)
        }
      })
    } catch (error) {
      console.error('[UploadQueueManager] 批量发送进度事件失败:', error)
      // 即使发送失败，数组也已经被清空，避免内存泄漏
    }
  }

  /**
   * 处理上传完成
   */
  private handleUploadComplete(taskId: number, data: any): void {
    this.uploadTaskService.update(taskId, {
      url: data.url,
      object_id: data.objectId || '',
      object_key: data.fileName || '',
      progress: 1.0,
      status: UploadTask.Status.COMPLETED,
      checkpoint_data: ''
    })

    // 减少Worker任务计数
    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      const currentCount = this.workerTaskCount.get(workerId) || 0
      this.workerTaskCount.set(workerId, Math.max(0, currentCount - 1))
    }

    this.taskWorkerMap.delete(taskId)
    console.log(`[UploadQueueManager] 任务 ${taskId} 上传完成: ${data.url}`)
  }

  /**
   * 处理上传错误
   */
  private handleUploadError(taskId: number, error: string): void {
    this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.FAILED, error)

    // 减少Worker任务计数
    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      const currentCount = this.workerTaskCount.get(workerId) || 0
      this.workerTaskCount.set(workerId, Math.max(0, currentCount - 1))
    }

    this.taskWorkerMap.delete(taskId)
    console.error(`[UploadQueueManager] 任务 ${taskId} 上传失败: ${error}`)
  }

  /**
   * 处理上传暂停
   */
  private handleUploadPaused(taskId: number, checkpoint?: any): void {
    // 更新任务状态为暂停，并保存 checkpoint 数据
    this.uploadTaskService.update(taskId, {
      status: UploadTask.Status.PAUSED,
      checkpoint_data: checkpoint ? JSON.stringify(checkpoint) : ''
    })

    console.log(`[UploadQueueManager] 任务 ${taskId} 已暂停${checkpoint ? '，已保存断点数据' : ''}`)
  }

  /**
   * 处理上传恢复
   */
  private async handleUploadResumed(taskId: number, _checkpoint?: any): Promise<void> {
    console.log(`[UploadQueueManager] 任务 ${taskId} 请求恢复`)

    // 清理当前的 Worker 映射，因为需要重新启动上传
    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      this.taskWorkerMap.delete(taskId)
      const currentCount = this.workerTaskCount.get(workerId) || 0
      this.workerTaskCount.set(workerId, Math.max(0, currentCount - 1))
    }

    // 更新任务状态为等待，让队列处理器重新处理
    this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING)

    console.log(`[UploadQueueManager] 任务 ${taskId} 已标记为等待重新上传`)
  }

  /**
   * 清理Worker（异常退出时调用）
   */
  private cleanupWorker(workerId: string): void {
    console.log(`[UploadQueueManager] 清理异常退出的Worker ${workerId}`)

    // 清理Worker相关数据
    this.workers.delete(workerId)
    this.workerLastUsed.delete(workerId)
    this.workerTaskCount.delete(workerId)

    // 清理相关的任务映射
    for (const [taskId, mappedWorkerId] of this.taskWorkerMap) {
      if (mappedWorkerId === workerId) {
        this.taskWorkerMap.delete(taskId)
        // 将任务状态重置为等待，以便重新分配
        this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING, 'Worker异常退出')
      }
    }
  }

  /**
   * 暂停上传任务
   */
  async pauseUpload(taskId: number): Promise<boolean> {
    console.log(`[UploadQueueManager] 接收到暂停任务请求: ${taskId}`)

    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      const worker = this.workers.get(workerId)
      if (worker) {
        console.log(`[UploadQueueManager] 向 Worker ${workerId} 发送暂停消息`)
        worker.postMessage({
          type: 'pause',
          taskId
        })
        return true
      } else {
        console.warn(`[UploadQueueManager] Worker ${workerId} 不存在`)
      }
    } else {
      console.warn(`[UploadQueueManager] 任务 ${taskId} 没有对应的 Worker，直接更新状态`)
      // 如果任务不在 Worker 中，直接更新状态为暂停
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PAUSED)
      return true
    }
    return false
  }

  /**
   * 恢复上传任务
   */
  async resumeUpload(taskId: number): Promise<boolean> {
    console.log(`[UploadQueueManager] 接收到恢复任务请求: ${taskId}`)

    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      const worker = this.workers.get(workerId)
      if (worker) {
        console.log(`[UploadQueueManager] 向 Worker ${workerId} 发送恢复消息`)
        worker.postMessage({
          type: 'resume',
          taskId
        })
        return true
      } else {
        console.warn(`[UploadQueueManager] Worker ${workerId} 不存在`)
      }
    } else {
      console.log(`[UploadQueueManager] 任务 ${taskId} 没有对应的 Worker，直接重新启动`)
      // 如果任务不在 Worker 中，直接更新状态为等待，让队列处理器重新处理
      this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING)
      return true
    }
    return false
  }

  /**
   * 取消上传任务
   */
  async cancelUpload(taskId: number): Promise<boolean> {
    const workerId = this.taskWorkerMap.get(taskId)
    if (workerId) {
      const worker = this.workers.get(workerId)
      if (worker) {
        worker.postMessage({
          type: 'cancel',
          taskId
        })
        this.taskWorkerMap.delete(taskId)
        return true
      }
    }
    return false
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): {
    initialized: boolean
    activeWorkers: number
    activeTasks: number
    maxWorkers: number
    workerTaskCounts: Record<string, number>
    progressBatchStatus: {
      currentSize: number
      maxSize: number
      hasTimer: boolean
    }
  } {
    const workerTaskCounts: Record<string, number> = {}
    for (const [workerId, count] of this.workerTaskCount) {
      workerTaskCounts[workerId] = count
    }

    return {
      initialized: this.initialized,
      activeWorkers: this.workers.size,
      activeTasks: this.taskWorkerMap.size,
      maxWorkers: this.MAX_WORKERS,
      workerTaskCounts,
      progressBatchStatus: {
        currentSize: this.progressEventBatch.length,
        maxSize: this.MAX_BATCH_SIZE,
        hasTimer: !!this.progressBatchTimer
      }
    }
  }

  /**
   * 启动Worker清理定时器
   */
  private startWorkerCleanup(): void {
    this.workerCleanupTimer = setInterval(() => {
      this.cleanupIdleWorkers()
    }, this.CLEANUP_INTERVAL)

    console.log(`[UploadQueueManager] Worker清理定时器已启动，间隔: ${this.CLEANUP_INTERVAL}ms`)
  }

  /**
   * 清理空闲的Worker
   */
  private cleanupIdleWorkers(): void {
    const now = Date.now()
    const workersToCleanup: string[] = []

    // 找出需要清理的空闲Worker
    for (const [workerId, lastUsed] of this.workerLastUsed) {
      const taskCount = this.workerTaskCount.get(workerId) || 0
      const idleTime = now - lastUsed

      // 如果Worker空闲且超过超时时间，标记为清理
      if (taskCount === 0 && idleTime > this.WORKER_IDLE_TIMEOUT) {
        workersToCleanup.push(workerId)
      }
    }

    // 清理空闲Worker，但至少保留一个Worker
    for (const workerId of workersToCleanup) {
      if (this.workers.size <= 1) {
        break // 保留最后一个Worker
      }
      this.terminateWorker(workerId)
    }

    if (workersToCleanup.length > 0) {
      console.log(`[UploadQueueManager] 清理了 ${workersToCleanup.length} 个空闲Worker，剩余: ${this.workers.size}`)
    }
  }

  /**
   * 查找空闲的Worker（任务数为0）
   */
  private findIdleWorker(): string | null {
    for (const [workerId, taskCount] of this.workerTaskCount) {
      if (taskCount === 0) {
        return workerId
      }
    }
    return null
  }

  /**
   * 查找任务数最少的Worker
   */
  private findLeastBusyWorker(): string | null {
    let leastBusyWorkerId: string | null = null
    let minTaskCount = Infinity

    for (const [workerId, taskCount] of this.workerTaskCount) {
      if (taskCount < minTaskCount) {
        minTaskCount = taskCount
        leastBusyWorkerId = workerId
      }
    }

    return leastBusyWorkerId
  }

  /**
   * 生成Worker ID
   */
  private generateWorkerId(): string {
    return `worker_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
  }

  /**
   * 更新Worker使用时间
   */
  private updateWorkerUsage(workerId: string): void {
    this.workerLastUsed.set(workerId, Date.now())
  }

  /**
   * 终止Worker进程
   */
  private terminateWorker(workerId: string): void {
    const worker = this.workers.get(workerId)
    if (worker) {
      console.log(`[UploadQueueManager] 终止Worker ${workerId}`)

      // 终止Worker进程
      worker.terminate()

      // 清理相关数据
      this.workers.delete(workerId)
      this.workerLastUsed.delete(workerId)
      this.workerTaskCount.delete(workerId)

      // 清理相关的任务映射
      const affectedTasks: number[] = []
      for (const [taskId, mappedWorkerId] of this.taskWorkerMap) {
        if (mappedWorkerId === workerId) {
          this.taskWorkerMap.delete(taskId)
          affectedTasks.push(taskId)
          // 将任务状态重置为等待，以便重新分配
          this.uploadTaskService.updateTaskStatus(taskId, UploadTask.Status.PENDING, 'Worker被清理')
        }
      }

      if (affectedTasks.length > 0) {
        console.log(`[UploadQueueManager] Worker ${workerId} 清理完成，影响任务: ${affectedTasks.join(', ')}`)
      }
    }
  }

  /**
   * NestJS 应用关闭时的生命周期钩子
   */
  async onApplicationShutdown(): Promise<void> {
    this.destroy()
  }

  /**
   * 获取 Worker 脚本路径
   */
  private getWorkerScriptPath(): string {
    try {
      // 检查是否在开发环境
      const isDev = process.env.NODE_ENV === 'development'

      if (isDev) {
        // 开发环境：使用相对于当前文件的路径
        const currentDir = dirname(fileURLToPath(import.meta.url))
        return join(currentDir, 'upload-worker.js')
      } else {
        // 生产环境：Worker 脚本在应用根目录的 resources/app.asar/node_modules/@app/main/dist/ 下
        // 或者在 resources/app/node_modules/@app/main/dist/ 下
        const appPath = app.getAppPath()

        // 尝试多个可能的路径
        const possiblePaths = [
          join(appPath, 'node_modules', '@app', 'main', 'dist', 'upload-worker.js'),
          join(appPath, 'packages', 'main', 'dist', 'upload-worker.js'),
          join(dirname(fileURLToPath(import.meta.url)), 'upload-worker.js'),
          join(process.cwd(), 'resources', 'app', 'node_modules', '@app', 'main', 'dist', 'upload-worker.js')
        ]

        for (const path of possiblePaths) {
          if (existsSync(path)) {
            this.logger.debug(`找到 Worker 脚本: ${path}`)
            return path
          }
        }

        // 如果都找不到，使用默认路径
        const defaultPath = join(appPath, 'node_modules', '@app', 'main', 'dist', 'upload-worker.js')
        this.logger.warn(`未找到 Worker 脚本，使用默认路径: ${defaultPath}`)
        return defaultPath
      }
    } catch (error) {
      // 如果无法获取路径，尝试使用相对路径
      this.logger.warn('无法获取 Worker 脚本路径，使用相对路径:', error)
      return './upload-worker.js'
    }
  }

  /**
   * 验证 Worker 脚本是否存在
   */
  private validateWorkerScript(): void {
    if (!existsSync(this.workerScriptPath)) {
      throw new Error(`Worker 脚本文件不存在: ${this.workerScriptPath}`)
    }
  }

  /**
   * 销毁队列管理器，清理所有资源
   */
  destroy(): void {
    console.log('[UploadQueueManager] 开始销毁队列管理器')

    // 清理定时器
    if (this.queueProcessorTimer) {
      clearInterval(this.queueProcessorTimer)
      this.queueProcessorTimer = undefined
    }

    if (this.workerCleanupTimer) {
      clearInterval(this.workerCleanupTimer)
      this.workerCleanupTimer = undefined
    }

    if (this.progressBatchTimer) {
      clearTimeout(this.progressBatchTimer)
      this.progressBatchTimer = undefined
    }

    // 如果还有未发送的进度事件，记录警告并清理
    if (this.progressEventBatch.length > 0) {
      console.warn(`[UploadQueueManager] 销毁时清理了 ${this.progressEventBatch.length} 个未发送的进度事件`)
      this.progressEventBatch = []
    }

    // 终止所有Worker
    const workerIds = Array.from(this.workers.keys())
    for (const workerId of workerIds) {
      this.terminateWorker(workerId)
    }

    // 清理所有状态
    this.workers.clear()
    this.taskWorkerMap.clear()
    this.workerLastUsed.clear()
    this.workerTaskCount.clear()

    this.initialized = false
    console.log('[UploadQueueManager] 队列管理器已销毁')
  }
}
