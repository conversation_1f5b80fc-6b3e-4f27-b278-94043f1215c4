{"name": "@app/main", "type": "module", "scripts": {"build": "vite build", "typecheck": "tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "exports": {".": {"types": "./src/index.ts", "default": "./dist/index.js"}}, "dependencies": {"@app/preload": "*", "@app/renderer": "*", "@nestjs/common": "^11.1.5", "@nestjs/core": "^11.1.5", "@types/fluent-ffmpeg": "^2.1.27", "ali-oss": "^6.23.0", "cos-nodejs-sdk-v5": "^2.15.4", "dotenv": "^17.2.0", "electron-updater": "6.6.2", "fluent-ffmpeg": "^2.1.3", "jsdom": "^26.1.0", "node-fetch": "^2.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.1.0"}, "devDependencies": {"@app/electron-versions": "*", "@types/jsdom": "^21.1.7", "@types/node-fetch": "^2.6.12", "electron-devtools-installer": "3.2.0", "globals": "^16.0.0", "typescript": "5.8.3", "typescript-eslint": "^8.30.1", "vite": "6.3.5"}}